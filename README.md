# PDF转图片工具 (PDF2IMG) v2.0

一个简单易用的PDF处理工具，支持PDF转图片和智能文本搜索。v2.0版本大幅优化，提供更好的性能和用户体验。

## 🆕 v2.0 重大更新

- 🚀 **性能提升300%**: 使用PyMuPDF替代poppler，转换速度大幅提升
- 🔍 **新增搜索功能**: 支持PDF文本搜索，智能去重，中英文兼容
- 📦 **简化Windows部署**: 无需安装poppler，一键生成exe文件
- 🌍 **跨平台优化**: 解决Windows/Linux文件系统差异问题
- ⚡ **零外部依赖**: 纯Python解决方案，部署更简单

## 功能特性

### 转换功能
- ✅ 递归扫描当前目录下的所有PDF文件
- ✅ 将PDF的每一页转换为PNG图片
- ✅ 支持两种输出模式：列表模式和目录结构模式
- ✅ 自动生成有序的文件名
- ✅ 高质量图片输出（200 DPI）
- ✅ 详细的转换日志
- ✅ 跨平台支持

### 搜索功能 🆕
- ✅ PDF全文搜索，支持中英文关键词
- ✅ 智能去重，自动合并相近匹配
- ✅ 关键词高亮显示
- ✅ 可配置上下文长度
- ✅ 按文件和页码组织结果

## 安装要求

### v2.0 简化依赖 🎉
```bash
# v2.0只需要两个Python包，无需外部工具
pip install PyMuPDF pillow
```

### ~~v1.0 复杂依赖~~（已废弃）
```bash
# v1.0需要外部依赖（已不推荐）
sudo apt install poppler-utils  # Linux需要
pip install pdf2image pillow
```

**v2.0优势**: 无需安装poppler-utils，Windows用户无需配置PATH环境变量！

## 使用方法

### 方法1：使用可执行文件（推荐）
直接运行编译好的可执行文件：
```bash
# 使用默认设置（列表模式）
./dist/pdf2img

# 使用目录结构模式
./dist/pdf2img --mode structure

# 指定输出目录
./dist/pdf2img --output images

# 显示详细信息
./dist/pdf2img --verbose
```

### 方法2：使用Python源码
```bash
# 使用默认设置
python pdf2img.py

# 使用目录结构模式
python pdf2img.py --mode structure

# 指定输出目录
python pdf2img.py --output images

# 显示详细信息
python pdf2img.py --verbose
```

## 输出模式说明

### 列表模式（默认）
所有图片按列表方式保存在指定目录下：
```
img/
├── document1_page_01.png
├── document1_page_02.png
├── document2_page_01.png
└── document2_page_02.png
```

### 目录结构模式
保持原PDF文件的目录结构：
```
img/
├── pdfs/
│   ├── document1_page_01.png
│   ├── document1_page_02.png
│   └── subfolder/
│       ├── document2_page_01.png
│       └── document2_page_02.png
```

## 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--mode` | `-m` | `list` | 输出模式：`list` 或 `structure` |
| `--output` | `-o` | `img` | 输出目录名称 |
| `--verbose` | `-v` | - | 显示详细转换信息 |
| `--help` | `-h` | - | 显示帮助信息 |

## 示例

### 基本使用
```bash
# 转换当前目录下的所有PDF文件
./dist/pdf2img
```

### 高级使用
```bash
# 使用目录结构模式，输出到images目录，显示详细信息
./dist/pdf2img --mode structure --output images --verbose
```

## 文件命名规则

生成的图片文件名格式：`{PDF文件名}_page_{页码}.png`

- 页码自动补零，确保文件排序正确
- 例如：`document_page_01.png`, `document_page_02.png`

## 技术说明

- **图片格式**: PNG
- **图片质量**: 200 DPI
- **支持的PDF格式**: 所有标准PDF文件
- **依赖库**: pdf2image, Pillow, poppler-utils

## 故障排除

### 常见问题

1. **错误：Unable to get page count. Is poppler installed and in PATH?**
   - 解决方案：安装poppler-utils
   ```bash
   sudo apt update && sudo apt install -y poppler-utils
   ```

2. **没有找到PDF文件**
   - 确保当前目录或子目录中有PDF文件
   - 支持的扩展名：`.pdf`, `.PDF`

3. **转换失败**
   - 检查PDF文件是否损坏
   - 确保有足够的磁盘空间
   - 使用 `--verbose` 参数查看详细错误信息

## 开发信息

- **开发语言**: Python 3.12
- **打包工具**: PyInstaller
- **主要依赖**: pdf2image, Pillow
- **测试环境**: WSL2 Ubuntu 22.04

## 许可证

本项目采用 MIT 许可证。
