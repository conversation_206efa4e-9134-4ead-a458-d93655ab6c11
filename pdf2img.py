#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转图片工具
支持将PDF文件转换为图片，支持多种输出模式
"""

import os
import sys
import argparse
import glob
from pathlib import Path
from typing import List, Tuple
import logging

try:
    from pdf2image import convert_from_path
    from PIL import Image
    PDF2IMAGE_AVAILABLE = True
except ImportError:
    PDF2IMAGE_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("pdf2image库未安装，请运行: pip install pdf2image pillow")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class PDF2ImageConverter:
    """PDF转图片转换器"""
    
    def __init__(self, output_mode: str = "list", output_dir: str = "img"):
        """
        初始化转换器
        
        Args:
            output_mode: 输出模式，"list"为列表方式，"structure"为目录结构方式
            output_dir: 输出目录名称
        """
        self.output_mode = output_mode
        self.output_dir = output_dir
        self.current_path = Path.cwd()
        self.output_path = self.current_path / output_dir
        
    def scan_pdf_files(self, scan_path: Path = None) -> List[Path]:
        """
        扫描指定路径下的所有PDF文件
        
        Args:
            scan_path: 扫描路径，默认为当前路径
            
        Returns:
            PDF文件路径列表
        """
        if scan_path is None:
            scan_path = self.current_path
            
        pdf_files = []
        
        # 递归查找所有PDF文件
        for pdf_file in scan_path.rglob("*.pdf"):
            if pdf_file.is_file():
                pdf_files.append(pdf_file)
                
        # 同时查找大写扩展名
        for pdf_file in scan_path.rglob("*.PDF"):
            if pdf_file.is_file():
                pdf_files.append(pdf_file)
                
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        return sorted(pdf_files)
    
    def create_output_directory(self):
        """创建输出目录"""
        self.output_path.mkdir(exist_ok=True)
        logger.info(f"输出目录: {self.output_path}")
    
    def get_output_filename(self, pdf_path: Path, page_num: int, total_pages: int) -> str:
        """
        生成输出文件名
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码（从1开始）
            total_pages: 总页数
            
        Returns:
            输出文件名
        """
        # 获取不带扩展名的文件名
        base_name = pdf_path.stem
        
        # 计算页码位数，确保文件名排序正确
        page_digits = len(str(total_pages))
        page_str = str(page_num).zfill(page_digits)
        
        return f"{base_name}_page_{page_str}.png"
    
    def get_output_path_for_file(self, pdf_path: Path) -> Path:
        """
        根据输出模式获取文件的输出路径
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            输出路径
        """
        if self.output_mode == "list":
            # 列表模式：所有图片直接保存在img目录下
            return self.output_path
        else:
            # 目录结构模式：保持原有的目录结构
            relative_path = pdf_path.parent.relative_to(self.current_path)
            return self.output_path / relative_path
    
    def convert_pdf_to_images(self, pdf_path: Path) -> bool:
        """
        将单个PDF文件转换为图片

        Args:
            pdf_path: PDF文件路径

        Returns:
            转换是否成功
        """
        if not PDF2IMAGE_AVAILABLE:
            logger.error("pdf2image库未安装，无法进行转换")
            return False

        try:
            logger.info(f"开始转换: {pdf_path}")

            # 获取输出路径
            output_dir = self.get_output_path_for_file(pdf_path)
            output_dir.mkdir(parents=True, exist_ok=True)

            # 转换PDF为图片
            try:
                # 使用pdf2image转换PDF
                images = convert_from_path(
                    str(pdf_path),
                    dpi=200,  # 设置DPI，影响图片质量
                    fmt='PNG'  # 输出格式
                )

                total_pages = len(images)
                logger.info(f"PDF共有 {total_pages} 页")

                # 保存每一页为图片
                for page_num, image in enumerate(images, 1):
                    # 生成输出文件名
                    filename = self.get_output_filename(pdf_path, page_num, total_pages)
                    output_file = output_dir / filename

                    # 保存图片
                    image.save(str(output_file), 'PNG', optimize=True)
                    logger.debug(f"保存页面 {page_num}: {output_file}")

                logger.info(f"转换完成: {pdf_path} -> {total_pages} 张图片")
                return True

            except Exception as pdf_error:
                logger.error(f"PDF转换错误 {pdf_path}: {str(pdf_error)}")
                return False

        except Exception as e:
            logger.error(f"转换失败 {pdf_path}: {str(e)}")
            return False
    
    def convert_all_pdfs(self) -> Tuple[int, int]:
        """
        转换所有PDF文件
        
        Returns:
            (成功数量, 总数量)
        """
        # 扫描PDF文件
        pdf_files = self.scan_pdf_files()
        
        if not pdf_files:
            logger.warning("未找到任何PDF文件")
            return 0, 0
        
        # 创建输出目录
        self.create_output_directory()
        
        # 转换所有PDF文件
        success_count = 0
        for pdf_file in pdf_files:
            if self.convert_pdf_to_images(pdf_file):
                success_count += 1
        
        logger.info(f"转换完成: {success_count}/{len(pdf_files)} 个文件成功")
        return success_count, len(pdf_files)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PDF转图片工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
输出模式说明:
  list      : 所有图片按列表方式保存在img目录下
  structure : 所有图片按原PDF文件目录结构保存

示例:
  python pdf2img.py                    # 使用默认设置
  python pdf2img.py --mode structure   # 使用目录结构模式
  python pdf2img.py --output images    # 指定输出目录为images
        """
    )
    
    parser.add_argument(
        "--mode", "-m",
        choices=["list", "structure"],
        default="list",
        help="输出模式 (默认: list)"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="img",
        help="输出目录名称 (默认: img)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建转换器并执行转换
    converter = PDF2ImageConverter(
        output_mode=args.mode,
        output_dir=args.output
    )
    
    try:
        success_count, total_count = converter.convert_all_pdfs()
        
        if total_count == 0:
            sys.exit(1)
        elif success_count == total_count:
            logger.info("所有PDF文件转换成功！")
            sys.exit(0)
        else:
            logger.warning(f"部分文件转换失败: {success_count}/{total_count}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
