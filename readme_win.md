
# convert_pdf.exe 使用说明

## 一、功能简介

将“当前目录及其所有子目录”中的 **PDF** 文件逐页转换为 **PNG** 图片。

- 支持两种输出模式：
  1. **flat**：所有图片集中输出到 `img/` 目录（默认）
  2. **tree**：保留原有目录层级再输出
- 支持自定义渲染分辨率（DPI）

## 二、准备工作

1. **将 `convert_pdf.exe` 放到需要批量转换的文件夹**（或任意位置，但程序只会扫描其“运行时所在目录”）。
2. 该 exe 已内置依赖库（PyMuPDF、Pillow），**无需再安装 Python 与第三方包**。
3. 确保 PDF 文件未被其他程序占用，否则会报“无法打开”错误。

## 三、基本用法

1. 在 Windows 资源管理器中进入放置 exe 的文件夹
2. 按住 `Shift` + 鼠标右键 → 选择“在此处打开 PowerShell/终端”
3. 输入命令：

```
convert_pdf.exe [--mode {flat,tree}] [--dpi DPI值]
```

**参数说明：**

- `--mode`
  - `flat`（默认）：全部图片统一输出到 `img\` 目录下各自文件夹
  - `tree`：在 `img\` 下重现原目录结构，例如  
    `原\子\example.pdf` → `img\原\子\example\example_p1.png`
- `--dpi`
  - 渲染分辨率，整数，默认 `150`
  - 常用取值：
    - 100：轻量预览
    - 150：默认兼顾速度/清晰度
    - 300：打印级

## 四、实例演示

**最简单调用（全部用默认值）：**

```
convert_pdf.exe
```

效果：在当前目录生成 `img\`，将发现的 PDF 依次拆页输出，分辨率 150 dpi，全部图片放在 `img\` 根下的各自子文件夹中。

**保留原目录结构且提高分辨率：**

```
convert_pdf.exe --mode tree --dpi 300
```

效果：`img\` 下复刻原目录层次，图片分辨率 300 dpi。

## 五、输出说明

- 图片格式：PNG，RGB，无透明通道
- 命名规则：`<原PDF文件名>_p<页码>.png`，页码从 1 开始
- 程序结束时会提示共处理多少个 PDF 以及输出目录位置

## 六、常见问题

- **Q1：输出后找不到图片？**  
  请检查当前目录是否包含 PDF，或者已用 `--mode tree` 导致图片在深层文件夹。
- **Q2：转换速度慢？**  
  DPI 设置越高越慢；也可将 exe 放到更快的硬盘分区运行。
- **Q3：能否转换加密 PDF？**  
  目前脚本未处理密码，需先解除加密后再执行。

## 七、退出与日志

运行过程中每转换完一个 PDF，控制台会输出：

```
[√] 路径\xxx.pdf  ->  输出目录
```

全部完成后给出总数。若要中途停止，可直接按 `Ctrl + C`。
