#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转图片工具 v2.0
支持将PDF文件转换为图片，支持多种输出模式
新增PDF文本搜索功能
使用PyMuPDF替代poppler，简化Windows部署
"""

import os
import sys
import argparse
import platform
import re
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import logging

try:
    import fitz  # PyMuPDF
    from PIL import Image
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("PyMuPDF库未安装，请运行: pip install PyMuPDF pillow")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class SearchResult:
    """搜索结果类"""
    
    def __init__(self, file_path: Path, page_num: int, text: str, context: str = ""):
        self.file_path = file_path
        self.page_num = page_num
        self.text = text
        self.context = context
    
    def __str__(self):
        return f"{self.file_path.name} - 第{self.page_num}页: {self.text}"


class PDF2ImageConverterV2:
    """PDF转图片转换器 v2.0"""
    
    def __init__(self, output_mode: str = "list", output_dir: str = "img"):
        """
        初始化转换器
        
        Args:
            output_mode: 输出模式，"list"为列表方式，"structure"为目录结构方式
            output_dir: 输出目录名称
        """
        self.output_mode = output_mode
        self.output_dir = output_dir
        self.current_path = Path.cwd()
        self.output_path = self.current_path / output_dir
        self.system = platform.system()
        
    def scan_pdf_files(self, scan_path: Path = None) -> List[Path]:
        """
        扫描指定路径下的所有PDF文件
        
        Args:
            scan_path: 扫描路径，默认为当前路径
            
        Returns:
            PDF文件路径列表
        """
        if scan_path is None:
            scan_path = self.current_path
            
        pdf_files = []
        
        # 递归查找所有PDF文件
        for pdf_file in scan_path.rglob("*.pdf"):
            if pdf_file.is_file():
                pdf_files.append(pdf_file)
                
        # 同时查找大写扩展名
        for pdf_file in scan_path.rglob("*.PDF"):
            if pdf_file.is_file():
                pdf_files.append(pdf_file)
                
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        return sorted(pdf_files)
    
    def create_output_directory(self):
        """创建输出目录"""
        self.output_path.mkdir(exist_ok=True)
        logger.info(f"输出目录: {self.output_path}")
    
    def get_output_filename(self, pdf_path: Path, page_num: int, total_pages: int) -> str:
        """
        生成输出文件名
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码（从1开始）
            total_pages: 总页数
            
        Returns:
            输出文件名
        """
        # 获取不带扩展名的文件名
        base_name = pdf_path.stem
        
        # 计算页码位数，确保文件名排序正确
        page_digits = len(str(total_pages))
        page_str = str(page_num).zfill(page_digits)
        
        return f"{base_name}_page_{page_str}.png"
    
    def get_output_path_for_file(self, pdf_path: Path) -> Path:
        """
        根据输出模式获取文件的输出路径
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            输出路径
        """
        if self.output_mode == "list":
            # 列表模式：所有图片直接保存在img目录下
            return self.output_path
        else:
            # 目录结构模式：保持原有的目录结构
            relative_path = pdf_path.parent.relative_to(self.current_path)
            return self.output_path / relative_path
    
    def convert_pdf_to_images(self, pdf_path: Path) -> bool:
        """
        将单个PDF文件转换为图片
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            转换是否成功
        """
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF库未安装，无法进行转换")
            return False
            
        try:
            logger.info(f"开始转换: {pdf_path}")
            
            # 获取输出路径
            output_dir = self.get_output_path_for_file(pdf_path)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 使用PyMuPDF打开PDF
            doc = fitz.open(str(pdf_path))
            total_pages = len(doc)
            logger.info(f"PDF共有 {total_pages} 页")
            
            # 转换每一页为图片
            for page_num in range(total_pages):
                page = doc.load_page(page_num)
                
                # 设置缩放比例以获得高质量图片 (相当于200 DPI)
                mat = fitz.Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                
                # 生成输出文件名
                filename = self.get_output_filename(pdf_path, page_num + 1, total_pages)
                output_file = output_dir / filename
                
                # 保存图片
                pix.save(str(output_file))
                logger.debug(f"保存页面 {page_num + 1}: {output_file}")
            
            doc.close()
            logger.info(f"转换完成: {pdf_path} -> {total_pages} 张图片")
            return True
            
        except Exception as e:
            logger.error(f"转换失败 {pdf_path}: {str(e)}")
            return False
    
    def search_in_pdf(self, pdf_path: Path, keyword: str, context_chars: int = 100) -> List[SearchResult]:
        """
        在PDF文件中搜索关键词

        Args:
            pdf_path: PDF文件路径
            keyword: 搜索关键词
            context_chars: 上下文字符数

        Returns:
            搜索结果列表
        """
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF库未安装，无法进行搜索")
            return []

        results = []

        try:
            doc = fitz.open(str(pdf_path))

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()

                # 使用正则表达式进行不区分大小写的搜索
                pattern = re.compile(re.escape(keyword), re.IGNORECASE)
                matches = list(pattern.finditer(text))

                if not matches:
                    continue

                # 合并相近的匹配，避免重复
                merged_matches = self._merge_nearby_matches(matches, context_chars)

                for match_group in merged_matches:
                    # 使用第一个匹配作为代表
                    first_match = match_group[0]
                    last_match = match_group[-1]

                    # 计算合并后的上下文范围
                    context_start = max(0, first_match.start() - context_chars)
                    context_end = min(len(text), last_match.end() + context_chars)
                    context = text[context_start:context_end].strip()

                    # 清理上下文中的换行符和多余空格
                    context = re.sub(r'\s+', ' ', context)

                    # 高亮所有匹配的关键词
                    highlighted_context = context
                    # 使用简单的字符串替换来高亮关键词
                    highlighted_context = re.sub(
                        re.escape(keyword),
                        f"**{keyword}**",
                        highlighted_context,
                        flags=re.IGNORECASE
                    )

                    result = SearchResult(
                        file_path=pdf_path,
                        page_num=page_num + 1,
                        text=f"{len(match_group)} 个匹配" if len(match_group) > 1 else first_match.group(),
                        context=highlighted_context
                    )
                    results.append(result)

            doc.close()

        except Exception as e:
            logger.error(f"搜索失败 {pdf_path}: {str(e)}")

        return results

    def _merge_nearby_matches(self, matches, context_chars: int):
        """
        合并相近的匹配，避免重复显示

        Args:
            matches: 匹配结果列表
            context_chars: 上下文字符数

        Returns:
            合并后的匹配组列表
        """
        if not matches:
            return []

        # 按位置排序
        sorted_matches = sorted(matches, key=lambda m: m.start())
        merged_groups = []
        current_group = [sorted_matches[0]]

        for match in sorted_matches[1:]:
            # 如果当前匹配与组内最后一个匹配的距离小于上下文长度的一半，则合并
            last_match = current_group[-1]
            if match.start() - last_match.end() < context_chars // 2:
                current_group.append(match)
            else:
                # 开始新的组
                merged_groups.append(current_group)
                current_group = [match]

        # 添加最后一组
        merged_groups.append(current_group)

        return merged_groups
    
    def search_all_pdfs(self, keyword: str) -> List[SearchResult]:
        """
        在所有PDF文件中搜索关键词
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            所有搜索结果
        """
        pdf_files = self.scan_pdf_files()
        all_results = []
        
        if not pdf_files:
            logger.warning("未找到任何PDF文件")
            return all_results
        
        logger.info(f"开始搜索关键词: '{keyword}'")
        
        for pdf_file in pdf_files:
            logger.debug(f"搜索文件: {pdf_file}")
            results = self.search_in_pdf(pdf_file, keyword)
            all_results.extend(results)
        
        logger.info(f"搜索完成，找到 {len(all_results)} 个匹配结果")
        return all_results
    
    def convert_all_pdfs(self) -> Tuple[int, int]:
        """
        转换所有PDF文件
        
        Returns:
            (成功数量, 总数量)
        """
        # 扫描PDF文件
        pdf_files = self.scan_pdf_files()
        
        if not pdf_files:
            logger.warning("未找到任何PDF文件")
            return 0, 0
        
        # 创建输出目录
        self.create_output_directory()
        
        # 转换所有PDF文件
        success_count = 0
        for pdf_file in pdf_files:
            if self.convert_pdf_to_images(pdf_file):
                success_count += 1
        
        logger.info(f"转换完成: {success_count}/{len(pdf_files)} 个文件成功")
        return success_count, len(pdf_files)


def print_search_results(results: List[SearchResult], keyword: str):
    """
    打印搜索结果

    Args:
        results: 搜索结果列表
        keyword: 搜索关键词
    """
    if not results:
        print(f"\n未找到包含关键词 '{keyword}' 的内容")
        return

    print(f"\n搜索关键词: '{keyword}'")
    print(f"找到 {len(results)} 个匹配结果:")
    print("=" * 80)

    # 按文件分组显示结果
    file_results = {}
    for result in results:
        file_name = result.file_path.name
        if file_name not in file_results:
            file_results[file_name] = []
        file_results[file_name].append(result)

    for file_name, file_results_list in file_results.items():
        print(f"\n📄 文件: {file_name}")
        print(f"   匹配数量: {len(file_results_list)}")

        for i, result in enumerate(file_results_list, 1):
            print(f"   [{i}] 第{result.page_num}页")
            # 显示匹配信息
            if "个匹配" in result.text:
                print(f"       匹配: {result.text}")
            else:
                print(f"       匹配: {result.text}")
            # 上下文已经包含高亮，直接显示
            print(f"       上下文: {result.context}")
            if i < len(file_results_list):
                print()

    print("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PDF转图片工具 v2.0 - 使用PyMuPDF，支持搜索功能",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
功能说明:
  convert   : 将PDF转换为图片
  search    : 在PDF中搜索关键词

输出模式说明:
  list      : 所有图片按列表方式保存在img目录下
  structure : 所有图片按原PDF文件目录结构保存

系统信息:
  当前系统: {platform.system()} {platform.release()}
  Python版本: {sys.version.split()[0]}

示例:
  # 转换功能
  python pdf2img_v2.py convert                    # 使用默认设置转换
  python pdf2img_v2.py convert --mode structure   # 使用目录结构模式
  python pdf2img_v2.py convert --output images    # 指定输出目录为images

  # 搜索功能
  python pdf2img_v2.py search "关键词"            # 搜索关键词
  python pdf2img_v2.py search "machine learning"  # 搜索英文关键词

Windows用户:
  pdf2img_v2.exe convert --verbose                # 使用exe文件转换
  pdf2img_v2.exe search "关键词"                  # 使用exe文件搜索
        """
    )

    # 添加子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 转换命令
    convert_parser = subparsers.add_parser('convert', help='转换PDF为图片')
    convert_parser.add_argument(
        "--mode", "-m",
        choices=["list", "structure"],
        default="list",
        help="输出模式 (默认: list)"
    )
    convert_parser.add_argument(
        "--output", "-o",
        default="img",
        help="输出目录名称 (默认: img)"
    )
    convert_parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )

    # 搜索命令
    search_parser = subparsers.add_parser('search', help='在PDF中搜索关键词')
    search_parser.add_argument(
        "keyword",
        help="要搜索的关键词"
    )
    search_parser.add_argument(
        "--context", "-c",
        type=int,
        default=100,
        help="上下文字符数 (默认: 100)"
    )
    search_parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )

    args = parser.parse_args()

    # 如果没有指定命令，默认为convert
    if not args.command:
        args.command = 'convert'
        args.mode = 'list'
        args.output = 'img'
        args.verbose = False

    # 设置日志级别
    if hasattr(args, 'verbose') and args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查PyMuPDF是否可用
    if not PYMUPDF_AVAILABLE:
        logger.error("PyMuPDF库未安装")
        logger.error("请运行: pip install PyMuPDF pillow")
        sys.exit(1)

    logger.info(f"运行环境: {platform.system()} {platform.release()}")
    logger.info(f"使用PyMuPDF v{fitz.version[0]} (无需外部依赖)")

    try:
        if args.command == 'convert':
            # 转换模式
            converter = PDF2ImageConverterV2(
                output_mode=args.mode,
                output_dir=args.output
            )

            success_count, total_count = converter.convert_all_pdfs()

            if total_count == 0:
                sys.exit(1)
            elif success_count == total_count:
                logger.info("所有PDF文件转换成功！")
                sys.exit(0)
            else:
                logger.warning(f"部分文件转换失败: {success_count}/{total_count}")
                sys.exit(1)

        elif args.command == 'search':
            # 搜索模式
            converter = PDF2ImageConverterV2()
            results = converter.search_all_pdfs(args.keyword)
            print_search_results(results, args.keyword)

            if results:
                sys.exit(0)
            else:
                sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
