@echo off
REM PDF2IMG Windows构建脚本
REM 请在Windows环境中运行此脚本

echo === PDF2IMG Windows构建脚本 ===
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python版本:
python --version
echo.

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到pip
    pause
    exit /b 1
)

REM 升级pip
echo 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 安装Python依赖...
pip install pdf2image pillow pyinstaller

REM 检查poppler是否安装（Windows版本）
echo.
echo 检查poppler依赖...
echo 注意: Windows需要手动安装poppler
echo 请从以下地址下载poppler-windows:
echo https://github.com/oschwartz10612/poppler-windows/releases/
echo 下载后解压到C:\poppler或其他目录，并将bin目录添加到PATH环境变量
echo.

REM 清理之前的构建
echo 清理之前的构建...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

REM 构建可执行文件
echo 构建Windows可执行文件...
pyinstaller --onefile --name pdf2img --console pdf2img.py

REM 检查构建结果
if exist "dist\pdf2img.exe" (
    echo.
    echo 构建成功！
    echo 可执行文件位置: dist\pdf2img.exe
    
    REM 显示文件大小
    for %%I in (dist\pdf2img.exe) do echo 文件大小: %%~zI 字节
    
    echo.
    echo 使用方法:
    echo   dist\pdf2img.exe --help
    echo   dist\pdf2img.exe --verbose
    echo.
    echo 注意: 运行前请确保已安装poppler并添加到PATH
) else (
    echo.
    echo 构建失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo 按任意键退出...
pause >nul
